{"_note1": "KEEP THIS FILE! Check it into a version control system (VCS) like git.", "_note2": "ObjectBox manages crucial IDs for your object model. See docs for details.", "_note3": "If you have VCS merge conflicts, you must resolve them according to ObjectBox docs.", "entities": [{"id": "1:4843187991762130878", "lastPropertyId": "11:8755141269492670503", "name": "UserLocalModel", "properties": [{"id": "2:8150247937050990135", "name": "id", "type": 9, "flags": 2080, "indexId": "1:6696137614632170115"}, {"id": "3:4867154126574018817", "name": "name", "type": 9}, {"id": "4:7808261147011250497", "name": "role", "type": 9}, {"id": "5:5861495493908320549", "name": "phone", "type": 9}, {"id": "6:4256530412587493788", "name": "email", "type": 9}, {"id": "7:4494853835445859077", "name": "description", "type": 9}, {"id": "8:330474981545105513", "name": "createdAt", "type": 10}, {"id": "9:5940803811252183204", "name": "updatedAt", "type": 10}, {"id": "10:5089396876425374590", "name": "oid", "type": 6, "flags": 1}], "relations": []}, {"id": "2:123456789012345678", "lastPropertyId": "20:3199587564168808968", "name": "InstanceLocalModel", "properties": [{"id": "1:111111111111111111", "name": "oid", "type": 6, "flags": 1}, {"id": "2:222222222222222222", "name": "id", "type": 9, "flags": 2080, "indexId": "2:222222222222222223"}, {"id": "3:333333333333333333", "name": "code", "type": 9}, {"id": "4:444444444444444444", "name": "type", "type": 9}, {"id": "5:555555555555555555", "name": "status", "type": 9}, {"id": "6:666666666666666666", "name": "survivorCode", "type": 9}, {"id": "7:777777777777777777", "name": "healthCenterId", "type": 9}, {"id": "8:888888888888888888", "name": "description", "type": 9}, {"id": "9:999999999999999999", "name": "createdAt", "type": 10}, {"id": "10:101010101010101010", "name": "updatedAt", "type": 10}, {"id": "11:111111111111111112", "name": "syncStatus", "type": 9}, {"id": "12:121212121212121212", "name": "localId", "type": 9, "flags": 2080, "indexId": "3:333333333333333334"}, {"id": "13:131313131313131313", "name": "lastSyncAttempt", "type": 10}, {"id": "14:141414141414141414", "name": "syncError", "type": 9}, {"id": "15:151515151515151515", "name": "isDeleted", "type": 1}, {"id": "16:2067695744753375725", "name": "relapsedAt", "type": 10}, {"id": "17:8917855125211569473", "name": "<PERSON><PERSON><PERSON>", "type": 9}, {"id": "18:1337283031199994753", "name": "healthCenterJson", "type": 9}, {"id": "19:3718597701250203606", "name": "relapse<PERSON><PERSON>", "type": 9}, {"id": "20:3199587564168808968", "name": "<PERSON><PERSON><PERSON>", "type": 9}], "relations": []}, {"id": "3:234567890123456789", "lastPropertyId": "13:213131313131313131", "name": "FollowupLocalModel", "properties": [{"id": "1:211111111111111111", "name": "oid", "type": 6, "flags": 1}, {"id": "2:222222222222222224", "name": "id", "type": 9, "flags": 2080, "indexId": "4:444444444444444445"}, {"id": "3:233333333333333333", "name": "title", "type": 9}, {"id": "4:244444444444444444", "name": "description", "type": 9}, {"id": "5:255555555555555555", "name": "companionId", "type": 9}, {"id": "6:266666666666666666", "name": "instanceId", "type": 9}, {"id": "7:277777777777777777", "name": "createdAt", "type": 10}, {"id": "8:288888888888888888", "name": "updatedAt", "type": 10}, {"id": "9:299999999999999999", "name": "syncStatus", "type": 9}, {"id": "10:210101010101010101", "name": "localId", "type": 9, "flags": 2080, "indexId": "5:555555555555555556"}, {"id": "11:211111111111111113", "name": "lastSyncAttempt", "type": 10}, {"id": "12:212121212121212121", "name": "syncError", "type": 9}, {"id": "13:213131313131313131", "name": "isDeleted", "type": 1}], "relations": []}, {"id": "5:2131990075469944008", "lastPropertyId": "12:5353034930472726044", "name": "CompanionLocalModel", "properties": [{"id": "1:5486570673802985772", "name": "oid", "type": 6, "flags": 1}, {"id": "2:5994209660998436109", "name": "id", "type": 9, "flags": 2080, "indexId": "6:7931814194615750109"}, {"id": "3:1941241111482350407", "name": "type", "type": 9}, {"id": "4:5260299708132266012", "name": "instanceId", "type": 9}, {"id": "5:5684648266782974248", "name": "createdAt", "type": 10}, {"id": "6:7907347413778994749", "name": "updatedAt", "type": 10}, {"id": "8:7796806207075825512", "name": "localId", "type": 9, "flags": 2080, "indexId": "7:7079359060673993916"}, {"id": "11:6913043116634890509", "name": "isDeleted", "type": 1}, {"id": "12:5353034930472726044", "name": "instance<PERSON>son", "type": 9}], "relations": []}, {"id": "7:1206948962258459754", "lastPropertyId": "15:6523415319098896387", "name": "HealthCenterLocalModel", "properties": [{"id": "1:3965859350121517998", "name": "oid", "type": 6, "flags": 1}, {"id": "2:8910810775081782794", "name": "id", "type": 9, "flags": 2080, "indexId": "10:3224088783981629108"}, {"id": "3:3811263425641669684", "name": "name", "type": 9}, {"id": "4:8889649924848815357", "name": "address", "type": 9}, {"id": "5:3155341395934483177", "name": "phone", "type": 9}, {"id": "6:3328532811955737804", "name": "servicesOffered", "type": 9}, {"id": "7:5311081021285726220", "name": "aps", "type": 6}, {"id": "8:1327041396974672556", "name": "companions", "type": 6}, {"id": "9:4030775453842690757", "name": "instances", "type": 6}, {"id": "10:4334806944805334056", "name": "relapses", "type": 6}, {"id": "11:633010979576058339", "name": "responsibility", "type": 9}, {"id": "12:1570670106292094135", "name": "healthZoneJson", "type": 9}, {"id": "13:4585350839257487354", "name": "createdAt", "type": 10}, {"id": "14:65781006731592663", "name": "updatedAt", "type": 10}, {"id": "15:6523415319098896387", "name": "lastSyncedAt", "type": 10}], "relations": []}, {"id": "8:5680527812754074453", "lastPropertyId": "6:3248450387396194659", "name": "HealthZoneLocalModel", "properties": [{"id": "1:9099099414556106493", "name": "oid", "type": 6, "flags": 1}, {"id": "2:2252304006184629140", "name": "id", "type": 9, "flags": 2080, "indexId": "11:5768413447315666547"}, {"id": "3:1111439678387543468", "name": "name", "type": 9}, {"id": "4:6531230281514491711", "name": "populationServed", "type": 6}, {"id": "5:2991545496507772860", "name": "createdAt", "type": 10}, {"id": "6:3248450387396194659", "name": "updatedAt", "type": 10}], "relations": []}, {"id": "9:3977560403868452195", "lastPropertyId": "11:3332868420746362183", "name": "CompanionRelapseLocalModel", "properties": [{"id": "1:5700778090457230225", "name": "oid", "type": 6, "flags": 1}, {"id": "2:4395371202231324200", "name": "id", "type": 9, "flags": 2080, "indexId": "20:3547353703867431408"}, {"id": "3:1544457817321428622", "name": "localId", "type": 9, "flags": 2080, "indexId": "21:8963891790248699014"}, {"id": "4:151088788059340459", "name": "companionId", "type": 9, "flags": 2048, "indexId": "22:5658318110842828008"}, {"id": "5:7779561520767444938", "name": "description", "type": 9}, {"id": "6:9045273673877848939", "name": "syncStatus", "type": 9}, {"id": "7:5676178513798200874", "name": "lastSyncAttempt", "type": 10}, {"id": "8:2968322266771921268", "name": "syncError", "type": 9}, {"id": "9:5559514721541433691", "name": "isDeleted", "type": 1}, {"id": "10:3972545048896093880", "name": "createdAt", "type": 10}, {"id": "11:3332868420746362183", "name": "updatedAt", "type": 10}], "relations": []}, {"id": "10:6809228688357094652", "lastPropertyId": "12:8067655157872396733", "name": "InstanceRelapseLocalModel", "properties": [{"id": "1:8636559884267916027", "name": "oid", "type": 6, "flags": 1}, {"id": "2:9221868290604143232", "name": "id", "type": 9, "flags": 2080, "indexId": "23:5775276047666512758"}, {"id": "3:7001970905617038198", "name": "localId", "type": 9, "flags": 2080, "indexId": "24:7727731150027515677"}, {"id": "5:8829017520108300061", "name": "description", "type": 9}, {"id": "6:4718181757980925303", "name": "syncStatus", "type": 9}, {"id": "7:7426645132041487092", "name": "lastSyncAttempt", "type": 10}, {"id": "8:6527944039596823871", "name": "syncError", "type": 9}, {"id": "9:3631081375662263007", "name": "isDeleted", "type": 1}, {"id": "10:4870987455641611355", "name": "createdAt", "type": 10}, {"id": "11:3280701053125573813", "name": "updatedAt", "type": 10}, {"id": "12:8067655157872396733", "name": "instanceLocalId", "type": 9, "flags": 2048, "indexId": "26:5367647898160623552"}], "relations": []}, {"id": "11:3164507662099933400", "lastPropertyId": "12:355905144806822505", "name": "InstanceCompanionLocalModel", "properties": [{"id": "1:3763901172611099787", "name": "oid", "type": 6, "flags": 1}, {"id": "2:6866793507894497094", "name": "id", "type": 9, "flags": 2080, "indexId": "12:1568671972986800250"}, {"id": "3:2022603295548964827", "name": "localId", "type": 9, "flags": 2080, "indexId": "13:6060253294180437095"}, {"id": "4:7035070993017972520", "name": "instanceLocalId", "type": 9, "flags": 2048, "indexId": "14:8540622414885268636"}, {"id": "5:1042704430004654935", "name": "type", "type": 9}, {"id": "6:7641415271262807680", "name": "userJson", "type": 9}, {"id": "7:1523234666763861504", "name": "syncStatus", "type": 9}, {"id": "8:6706543861244143601", "name": "lastSyncAttempt", "type": 10}, {"id": "9:7057319490283205365", "name": "syncError", "type": 9}, {"id": "10:8160391148219200306", "name": "isDeleted", "type": 1}, {"id": "11:2309829828985691886", "name": "createdAt", "type": 10}, {"id": "12:355905144806822505", "name": "updatedAt", "type": 10}], "relations": []}, {"id": "12:2822108779830572564", "lastPropertyId": "12:6803541314689736033", "name": "MemberLocalModel", "properties": [{"id": "1:1234962094324276160", "name": "oid", "type": 6, "flags": 1}, {"id": "2:4925758493464560538", "name": "id", "type": 9, "flags": 2048, "indexId": "15:4162002983318571608"}, {"id": "3:2981032850090524115", "name": "name", "type": 9}, {"id": "4:3689744126947909949", "name": "email", "type": 9}, {"id": "5:2991002803398878388", "name": "role", "type": 9}, {"id": "6:14672183895157632", "name": "responsibility", "type": 9}, {"id": "7:1008441726396594711", "name": "companionRole", "type": 9}, {"id": "8:148444596010105156", "name": "phone", "type": 9}, {"id": "9:3063610174526907481", "name": "description", "type": 9}, {"id": "10:3727997205887903228", "name": "createdAt", "type": 10}, {"id": "11:5660630452644298254", "name": "updatedAt", "type": 10}, {"id": "12:6803541314689736033", "name": "healthCenterId", "type": 9, "flags": 2048, "indexId": "19:3406569888483827386"}], "relations": []}, {"id": "14:8930856652812031345", "lastPropertyId": "8:7401302589380796055", "name": "QuestionnaireLocalModel", "properties": [{"id": "1:277974317547909251", "name": "oid", "type": 6, "flags": 1}, {"id": "2:44398720361841798", "name": "id", "type": 9, "flags": 2080, "indexId": "18:9196782966579820819"}, {"id": "3:5442426107910623341", "name": "question", "type": 9}, {"id": "4:3920658988480017100", "name": "type", "type": 9}, {"id": "5:9094428199092741294", "name": "hint", "type": 9}, {"id": "6:6555616267116442702", "name": "createdAt", "type": 10}, {"id": "7:2965190484639975832", "name": "updatedAt", "type": 10}, {"id": "8:7401302589380796055", "name": "<PERSON><PERSON><PERSON>", "type": 9}], "relations": []}], "lastEntityId": "14:8930856652812031345", "lastIndexId": "26:5367647898160623552", "lastRelationId": "0:0", "lastSequenceId": "0:0", "modelVersion": 5, "modelVersionParserMinimum": 5, "retiredEntityUids": [7881812106990892811, 1945190895619663006, 1384164074911263481], "retiredIndexUids": [826561280328193907], "retiredPropertyUids": [2732646462392475608, 8755141269492670503, 4879820946144288907, 8272258262473875575, 6581317629370286128, 5606941273233180838, 1302224599751260177, 3454574729843236120, 5189089791841038012, 832552586866868912, 3521151883837436064, 8340965384149234714, 7180449859713486640, 1052031254800829878, 3304613512950611961, 4770782705386923279, 5649284891491276068, 5737624744565037624, 5705483735001706751, 8262436783262865713, 4827601966653328459, 7917887324526627988, 3646374292155004746, 2959536292104268861, 7036360107462867196, 6309937417859282637, 7626130646663799528, 3401376047370408258, 2216431904208992702, 986515967099688258, 313681023087369864, 1735192707651803392, 8108515293488047994, 1643234975298268111, 718983673961206878, 7795727801655358653], "retiredRelationUids": [], "version": 1}
import 'dart:convert';
import 'dart:developer';

import 'package:injectable/injectable.dart' hide Order;
import 'package:s3g/core/objectbox/objectbox.dart';
import 'package:s3g/src/companion/companion/data/local/models/companion_local_model.dart';
import 'package:s3g/src/companion/relapse/relapse.dart' show RelapseModel;
import 'package:s3g/src/manager/member/member.dart';

abstract class CompanionLocalDataSource {
  Future<List<CompanionLocalModel>> getAllCompanions();
  Future<List<CompanionLocalModel>> getCompanionsByInstance(String instanceId);
  Future<CompanionLocalModel?> getCompanionById(String id);
  Future<CompanionLocalModel?> getCompanionByLocalId(String localId);
  Future<void> saveCompanion(CompanionLocalModel companion);
  Future<void> saveCompanions(List<CompanionLocalModel> companions);
  Future<void> deleteCompanion(String localId);
  Future<void> deleteCompanionById(String id);
  Future<void> clearAllCompanions();
  Future<void> removeRelapseFromCompanionInstance(String companionId);
  Future<void> addRelapseToCompanionInstance(
    String companionId,
    RelapseModel relapse,
  );
  Future<void> updateCompanionFromServer(CompanionLocalModel serverCompanion);
  Future<void> updateCompanionContent(
    String localId, {
    String? type,
    String? instanceId,
  });
}

@Injectable(as: CompanionLocalDataSource)
class CompanionLocalDataSourceImpl implements CompanionLocalDataSource {
  final ObjectBox _objectBox;

  CompanionLocalDataSourceImpl({required ObjectBox objectBox})
      : _objectBox = objectBox;

  Box<CompanionLocalModel> get _box =>
      _objectBox.store.box<CompanionLocalModel>();

  @override
  Future<List<CompanionLocalModel>> getAllCompanions() async {
    final query = _box
        .query(CompanionLocalModel_.isDeleted.equals(false))
        .order(CompanionLocalModel_.createdAt, flags: Order.descending)
        .build();

    final companions = await query.findAsync();
    query.close();

    return companions;
  }

  @override
  Future<List<CompanionLocalModel>> getCompanionsByInstance(
      String instanceId) async {
    final query = _box
        .query(CompanionLocalModel_.instanceId.equals(instanceId) &
            CompanionLocalModel_.isDeleted.equals(false))
        .build();
    final companions = await query.findAsync();
    query.close();
    return companions;
  }

  @override
  Future<CompanionLocalModel?> getCompanionById(String id) async {
    final query = _box
        .query(CompanionLocalModel_.id.equals(id) &
            CompanionLocalModel_.isDeleted.equals(false))
        .build();
    final companion = await query.findFirstAsync();
    query.close();
    return companion;
  }

  @override
  Future<CompanionLocalModel?> getCompanionByLocalId(String localId) async {
    final query = _box
        .query(CompanionLocalModel_.localId.equals(localId) &
            CompanionLocalModel_.isDeleted.equals(false))
        .build();
    final companion = await query.findFirstAsync();
    query.close();
    return companion;
  }

  @override
  Future<void> saveCompanion(CompanionLocalModel companion) async {
    CompanionLocalModel? existing;

    // First, check if a companion with this server ID already exists
    if (companion.id != null) {
      final queryById =
          _box.query(CompanionLocalModel_.id.equals(companion.id!)).build();
      existing = await queryById.findFirstAsync();
      queryById.close();
    }

    // If not found by server ID, check by localId
    if (existing == null) {
      final queryByLocalId = _box
          .query(CompanionLocalModel_.localId.equals(companion.localId))
          .build();
      existing = await queryByLocalId.findFirstAsync();
      queryByLocalId.close();
    }

    if (existing != null) {
      // Update the existing record, preserving the ObjectBox ID
      existing.id = companion.id; // Update server ID if it was assigned
      existing.type = companion.type;
      existing.instanceId = companion.instanceId;
      existing.instanceJson = companion.instanceJson;
      existing.createdAt = companion.createdAt;
      existing.updatedAt = companion.updatedAt;
      existing.isDeleted = companion.isDeleted;
      await _box.putAsync(existing);
      return;
    }

    // No existing record found, save as new
    await _box.putAsync(companion);
  }

  @override
  Future<void> saveCompanions(List<CompanionLocalModel> companions) async {
    // Process each companion individually to handle updates
    for (final companion in companions) {
      await saveCompanion(companion);
    }
  }

  @override
  Future<void> deleteCompanion(String localId) async {
    final companion = await getCompanionByLocalId(localId);
    if (companion != null) {
      companion.isDeleted = true;
      companion.updatedAt = DateTime.now();
      await saveCompanion(companion);
    }
  }

  @override
  Future<void> deleteCompanionById(String id) async {
    final companion = await getCompanionById(id);
    if (companion != null) {
      companion.isDeleted = true;
      companion.updatedAt = DateTime.now();
      await saveCompanion(companion);
    }
  }

  @override
  Future<void> clearAllCompanions() async {
    await _box.removeAllAsync();
  }

  @override
  Future<void> updateCompanionFromServer(
    CompanionLocalModel serverCompanion,
  ) async {
    // Check if we have a local version by server ID
    final existingById = serverCompanion.id != null
        ? await getCompanionById(serverCompanion.id!)
        : null;

    if (existingById != null) {
      // Only update if server version is newer
      if (serverCompanion.updatedAt.isAfter(existingById.updatedAt)) {
        // Update existing companion with server data, preserving the ObjectBox ID
        existingById.type = serverCompanion.type;
        existingById.instanceId = serverCompanion.instanceId;
        existingById.instanceJson = serverCompanion.instanceJson;
        existingById.updatedAt = serverCompanion.updatedAt;
        await saveCompanion(existingById);
      }
    } else {
      // Check if we have any companion with the same ID (including deleted ones)
      final query = _box
          .query(CompanionLocalModel_.id.equals(serverCompanion.id!))
          .build();

      final conflictingCompanion = await query.findFirstAsync();
      query.close();

      if (conflictingCompanion != null) {
        // Only update if server version is newer
        if (serverCompanion.updatedAt.isAfter(conflictingCompanion.updatedAt)) {
          // Update the existing companion (even if deleted) instead of creating a new one
          conflictingCompanion.type = serverCompanion.type;
          conflictingCompanion.instanceId = serverCompanion.instanceId;
          conflictingCompanion.instanceJson = serverCompanion.instanceJson;
          conflictingCompanion.createdAt = serverCompanion.createdAt;
          conflictingCompanion.updatedAt = serverCompanion.updatedAt;
          conflictingCompanion.isDeleted = false; // Resurrect if it was deleted
          await saveCompanion(conflictingCompanion);
        }
      } else {
        // Save new companion from server
        await saveCompanion(serverCompanion);
      }
    }
  }

  @override
  Future<void> updateCompanionContent(
    String localId, {
    String? type,
    String? instanceId,
  }) async {
    final companion = await getCompanionByLocalId(localId);
    if (companion != null) {
      // Convert type string to MemberCompanionRole if provided
      if (type != null) {
        try {
          final roleType = MemberCompanionRole.values.firstWhere(
            (e) => e.name == type,
          );
          companion.updateContent(
            newType: roleType,
            newInstanceId: instanceId,
          );
        } catch (e) {
          log('Invalid companion type: $type');
          return;
        }
      } else if (instanceId != null) {
        companion.updateContent(newInstanceId: instanceId);
      }

      await saveCompanion(companion);
    }
  }

  @override
  Future<void> removeRelapseFromCompanionInstance(String companionId) async {
    final companion = await getCompanionById(companionId);
    if (companion != null) {
      final instanceMap =
          jsonDecode(companion.instanceJson) as Map<String, dynamic>;

      var modified = false;

      if (instanceMap.containsKey('relapse')) {
        instanceMap.remove('relapse');
        modified = true;
      }

      if (instanceMap.containsKey('relapsedAt')) {
        instanceMap['relapsedAt'] = null;
        modified = true;
      }

      if (modified) {
        companion.instanceJson = jsonEncode(instanceMap);
        saveCompanion(companion);
      }
    }
  }

  @override
  Future<void> addRelapseToCompanionInstance(
    String companionId,
    RelapseModel relapse,
  ) async {
    final companion = await getCompanionById(companionId);
    if (companion != null) {
      final instanceMap =
          jsonDecode(companion.instanceJson) as Map<String, dynamic>;

      instanceMap['relapse'] = relapse.toJson();
      instanceMap['relapsedAt'] = relapse.createdAt.toIso8601String();

      companion.instanceJson = jsonEncode(instanceMap);
      saveCompanion(companion);
    }
  }
}

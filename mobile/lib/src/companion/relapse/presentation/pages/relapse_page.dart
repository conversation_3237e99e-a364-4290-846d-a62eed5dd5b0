import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:getwidget/getwidget.dart';
import 'package:intl/intl.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/common/widgets/sync_status_widget.dart';
import 'package:s3g/core/config/theme.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/src/companion/companion/companion.dart';
import 'package:s3g/src/companion/relapse/presentation/bloc/relapse_cubit.dart';
import 'package:s3g/src/manager/instance_features/relapse/relapse.dart'
    show Relapse;

class RelapsePage extends StatelessWidget {
  const RelapsePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color(0xFFF8FAFC),
      child: SingleChildScrollView(
        padding: bodyPadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Modern Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(cardRadius),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.04),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: AppTheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.trending_down_outlined,
                      color: AppTheme.primary,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  const Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "Gestion des rechutes",
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF1F2937),
                          ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          "Signaler et gérer les épisodes de rechute",
                          style: TextStyle(
                            fontSize: 14,
                            color: Color(0xFF6B7280),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SyncStatusWidget(),
                ],
              ),
            ),

            const SizedBox(height: 20),

            BlocBuilder<CompanionShowCubit, CompanionShowState>(
              builder: (_, state) {
                switch (state) {
                  case CompanionShowLoaded(companion: Companion companion):
                    return _RelapsePageContentWrapper(companion);
                  default:
                }

                return const SizedBox.shrink();
              },
            ),
          ],
        ),
      ),
    );
  }
}

class _RelapsePageContentWrapper extends StatelessWidget {
  final Companion companion;

  const _RelapsePageContentWrapper(this.companion);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      key: ValueKey(companion.id),
      create: (context) {
        return RelapseCubit(
          companion.id,
          createRelapseUseCase: getIt(),
          updateRelapseUseCase: getIt(),
          deleteRelapseUseCase: getIt(),
          getRelapseUseCase: getIt(),
          connectionChecker: getIt(),
        )..getRelapse();
      },
      child: _RelapsePageContent(companion),
    );
  }
}

class _RelapsePageContent extends StatefulWidget {
  final Companion companion;

  const _RelapsePageContent(this.companion);

  @override
  State<_RelapsePageContent> createState() => _RelapsePageContentState();
}

class _RelapsePageContentState extends State<_RelapsePageContent> {
  bool _editMode = false;

  Relapse? _relapse;
  StreamSubscription<RelapseState>? _subscription;

  @override
  void initState() {
    _relapse = widget.companion.instance.relapse;

    _subscription = context.read<RelapseCubit>().stream.listen((state) {
      if (state is RelapseSuccess) {
        _relapse = state.relapse;
        setState(() {});
      }
    });

    super.initState();
  }

  @override
  void dispose() {
    _subscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_relapse != null && !_editMode) {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(cardRadius),
          border: Border.all(
            color: AppTheme.primary.withValues(alpha: 0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.04),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with edit button
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: AppTheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    Icons.warning_outlined,
                    color: AppTheme.primary,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        "Rechute signalée",
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF1F2937),
                        ),
                      ),
                      Text(
                        DateFormat.yMMMd('fr').format(
                          _relapse!.createdAt.toLocal(),
                        ),
                        style: TextStyle(
                          fontSize: 14,
                          color: AppTheme.primary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () {
                    setState(() {
                      _editMode = true;
                    });
                  },
                  icon: const Icon(
                    Icons.edit_outlined,
                    color: Color(0xFF6B7280),
                  ),
                  tooltip: "Modifier",
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Description
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                _relapse!.description,
                style: const TextStyle(
                  fontSize: 14,
                  color: Color(0xFF374151),
                  height: 1.5,
                ),
              ),
            ),

            const SizedBox(height: 20),

            // Delete button
            BlocBuilder<RelapseCubit, RelapseState>(
              builder: (_, state) {
                if (state is RelapseLoading) {
                  return const Center(
                    child: Padding(
                      padding: EdgeInsets.all(16),
                      child: GFLoader(size: GFSize.SMALL),
                    ),
                  );
                }

                return SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () async {
                      if (await pressConfirm(context)) {
                        return _deleteRelapse();
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                      "Supprimer le signalement",
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      );
    }

    return _RelapsePageForm(
      key: ValueKey(_editMode),
      relapse: _relapse,
      editMode: _editMode,
      closeEditMode: () {
        setState(() {
          _editMode = false;
        });
      },
    );
  }

  void _deleteRelapse() async {
    await context.read<RelapseCubit>().deleteRelapse();

    if (mounted) {
      await context.read<CompanionShowCubit>().getFetchCompanion();
    }

    if (mounted) {
      context.read<RefreshDataBloc>().add(RefreshDataCompanionsEvent());
    }

    setState(() {
      _editMode = false;
    });
  }
}

class _RelapsePageForm extends StatefulWidget {
  final bool editMode;
  final Relapse? relapse;
  final void Function() closeEditMode;

  const _RelapsePageForm({
    super.key,
    required this.relapse,
    required this.editMode,
    required this.closeEditMode,
  });

  @override
  State<_RelapsePageForm> createState() => _RelapsePageFormState();
}

class _RelapsePageFormState extends State<_RelapsePageForm> {
  bool _formEnabled = false;
  final _textController = TextEditingController();

  @override
  void initState() {
    _textController.text = widget.relapse?.description ?? '';

    super.initState();
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final relapseBlocState = context.watch<RelapseCubit>().state;

    if (!widget.editMode && !_formEnabled) {
      return Container(
        padding: const EdgeInsets.all(40),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(cardRadius),
          border: Border.all(
            color: Colors.grey.withValues(alpha: 0.1),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.04),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Icon(
              Icons.report_problem_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 20),
            const Text(
              "Aucune rechute signalée",
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF1F2937),
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              "Vous pouvez signaler un épisode de rechute en cliquant sur le bouton ci-dessous.",
              style: TextStyle(
                fontSize: 14,
                color: Color(0xFF6B7280),
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  setState(() {
                    _formEnabled = true;
                  });
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text(
                  "Signaler une rechute",
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(cardRadius),
        border: Border.all(
          color: AppTheme.primary.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: AppTheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  Icons.edit_note,
                  color: AppTheme.primary,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.editMode
                          ? "Modifier la rechute"
                          : "Signaler une rechute",
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF1F2937),
                      ),
                    ),
                    const Text(
                      "Veuillez décrire la situation",
                      style: TextStyle(
                        fontSize: 14,
                        color: Color(0xFF6B7280),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Text input
          TextField(
            controller: _textController,
            decoration: InputDecoration(
              labelText: 'Description de la rechute',
              hintText:
                  'Décrivez les circonstances et détails de la rechute...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: Colors.grey.withValues(alpha: 0.3),
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: AppTheme.primary,
                  width: 2,
                ),
              ),
              filled: true,
              fillColor: Colors.grey.withValues(alpha: 0.02),
            ),
            keyboardType: TextInputType.multiline,
            minLines: 4,
            maxLines: 8,
          ),

          const SizedBox(height: 20),

          // Listener for errors and success
          BlocListener<RelapseCubit, RelapseState>(
            listener: (_, state) {
              switch (state) {
                case RelapseSuccess(
                    isOffline: final isOffline,
                    successType: final successType
                  ):
                  if (successType == SuccessType.get) break;

                  showToast(
                    context,
                    message: isOffline
                        ? 'Rechute enregistrée hors ligne. Elle sera synchronisée automatiquement.'
                        : 'Rechute enregistrée avec succès',
                    type: ToastType.success,
                  );
                  break;

                case RelapseError():
                  showToast(
                    context,
                    message: state.message,
                    type: ToastType.error,
                  );
                  break;

                default:
              }
            },
            child: const SizedBox.shrink(),
          ),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    if (widget.editMode) {
                      widget.closeEditMode();
                    } else {
                      setState(() {
                        _formEnabled = false;
                      });
                    }
                  },
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    side: BorderSide(
                      color: Colors.grey.withValues(alpha: 0.3),
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    "Annuler",
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF6B7280),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton(
                  onPressed: relapseBlocState is RelapseLoading ? null : _save,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: relapseBlocState is RelapseLoading
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Text(
                          widget.editMode ? "Modifier" : "Signaler",
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _save() async {
    // Create or update relapse status
    if (widget.relapse != null) {
      // Update existing relapse
      await context
          .read<RelapseCubit>()
          .updateRelapse(description: _textController.value.text);
    } else {
      // Create new relapse
      await context
          .read<RelapseCubit>()
          .createRelapse(description: _textController.value.text);
    }

    // Refresh companion
    if (mounted) {
      context.read<RelapseCubit>().getRelapse();
      context.read<CompanionShowCubit>().getFetchCompanion();
    }

    // Refresh companions list
    if (mounted) {
      context.read<RefreshDataBloc>().add(RefreshDataCompanionsEvent());
    }

    widget.closeEditMode();
  }
}

import 'dart:convert';

import 'package:objectbox/objectbox.dart';
import 'package:s3g/src/authentication/authentication.dart';
import 'package:s3g/src/manager/health_center/health_center.dart';
import 'package:s3g/src/manager/instance/data/remote/models/instance_model.dart';
import 'package:s3g/src/manager/instance/data/remote/models/survivor_model.dart';
import 'package:s3g/src/manager/instance/domain/entity/instance.dart';
import 'package:s3g/src/manager/instance_features/relapse/relapse.dart';
import 'package:s3g/src/sync/sync_service.dart' show SyncStatus;
import 'package:uuid/uuid.dart';

@Entity()
class InstanceLocalModel {
  @Id()
  int oid;

  @Index()
  @Unique()
  String? id; // Server ID, null for offline-created instances

  String code;
  String type; // InstanceType enum as string
  String status; // InstanceStatus enum as string
  String survivorCode;
  String healthCenterId;
  String? description;

  @Property(type: PropertyType.date)
  late DateTime createdAt;

  @Property(type: PropertyType.date)
  late DateTime updatedAt;

  String syncStatus; // SyncStatus enum as string

  @Index()
  @Unique()
  String localId; // Local UUID for offline instances

  @Property(type: PropertyType.date)
  DateTime? lastSyncAttempt;

  String? syncError;
  bool isDeleted;

  @Property(type: PropertyType.date)
  DateTime? relapsedAt;

  // Additional fields from InstanceShow - stored as JSON
  String? survivorJson;
  String? healthCenterJson;
  String? relapseJson;
  String? creatorJson;

  InstanceLocalModel({
    this.oid = 0,
    this.id,
    required this.code,
    required this.type,
    required this.status,
    required this.survivorCode,
    required this.healthCenterId,
    required this.localId,
    this.description,
    required this.createdAt,
    required this.updatedAt,
    this.syncStatus = 'pending',
    this.lastSyncAttempt,
    this.syncError,
    this.isDeleted = false,
    this.relapsedAt,
    this.survivorJson,
    this.healthCenterJson,
    this.relapseJson,
    this.creatorJson,
  });

  // Convert from domain entity
  factory InstanceLocalModel.fromEntity(Instance instance) {
    return InstanceLocalModel(
      id: instance.id,
      localId: const Uuid().v4(),
      code: instance.code,
      type: instance.type.name,
      status: instance.status.name,
      survivorCode: instance.survivorCode,
      healthCenterId: instance.healthCenterId,
      description: instance.description,
      createdAt: instance.createdAt,
      updatedAt: instance.updatedAt,
      syncStatus: SyncStatus.synced.value,
      relapsedAt: instance.relapsedAt,
    );
  }

  // Convert from InstanceShow entity - serialize optional fields to JSON
  factory InstanceLocalModel.fromInstanceShow(InstanceShow instance) {
    return InstanceLocalModel(
      id: instance.id,
      localId: const Uuid().v4(),
      code: instance.code,
      type: instance.type.name,
      status: instance.status.name,
      survivorCode: instance.survivorCode,
      healthCenterId: instance.healthCenterId,
      description: instance.description,
      createdAt: instance.createdAt,
      updatedAt: instance.updatedAt,
      syncStatus: SyncStatus.synced.value,
      relapsedAt: instance.relapsedAt,
      // Convert optional fields to JSON strings for storage
      survivorJson: _survivorToJson(instance.survivor),
      healthCenterJson: _healthCenterToJson(instance.healthCenter),
      relapseJson: _relapseToJson(instance.relapse),
      creatorJson: _userToJson(instance.creator),
    );
  }

  // Alternative factory method when we have JSON data directly (e.g., from API response)
  factory InstanceLocalModel.fromInstanceShowJson(Map<String, dynamic> json) {
    final instanceShow = InstanceShowModel.fromJson(json);
    return InstanceLocalModel.fromInstanceShow(instanceShow);
  }

  // Create offline instance
  factory InstanceLocalModel.createOffline({
    required String survivorCode,
    required InstanceType type,
    required String healthCenterId,
    String? description,
    DateTime? relapsedAt,
  }) {
    final now = DateTime.now();
    final uuid = const Uuid().v4();
    return InstanceLocalModel(
      code: 'OFFLINE-${uuid.substring(0, 8).toUpperCase()}',
      type: type.name,
      localId: uuid,
      status: InstanceStatus.OPEN.name,
      survivorCode: survivorCode,
      healthCenterId: healthCenterId,
      description: description,
      createdAt: now,
      updatedAt: now,
      syncStatus: SyncStatus.pending.value,
      relapsedAt: relapsedAt,
    );
  }

  // Convert to domain entity (Instance or InstanceShow)
  Instance toEntity() {
    return Instance(
      id: id ?? localId, // Use localId if no server ID
      code: code,
      type: InstanceType.values.firstWhere((t) => t.name == type),
      status: InstanceStatus.values.firstWhere((s) => s.name == status),
      survivorCode: survivorCode,
      relapsedAt: relapsedAt,
      healthCenterId: healthCenterId,
      description: description,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  // Convert to InstanceShow entity with optional fields
  InstanceShow toInstanceShow() {
    try {
      // Deserialize optional JSON data using model classes
      Survivor? survivor;
      HealthCenter? healthCenter;
      Relapse? relapse;
      User? creator;

      if (survivorJson != null) {
        final survivorData = jsonDecode(survivorJson!) as Map<String, dynamic>;
        survivor = SurvivorModel.fromJson(survivorData);
      }

      if (healthCenterJson != null) {
        final healthCenterData =
            jsonDecode(healthCenterJson!) as Map<String, dynamic>;
        healthCenter = HealthCenterModel.fromJson(healthCenterData);
      }

      if (relapseJson != null) {
        final relapseData = jsonDecode(relapseJson!) as Map<String, dynamic>;
        relapse = RelapseModel.fromJson(relapseData);
      }

      if (creatorJson != null) {
        final creatorData = jsonDecode(creatorJson!) as Map<String, dynamic>;
        creator = UserModel.fromJson(creatorData);
      }

      return InstanceShow(
        id: id ?? localId,
        code: code,
        type: InstanceType.values.firstWhere((t) => t.name == type),
        status: InstanceStatus.values.firstWhere((s) => s.name == status),
        survivorCode: survivorCode,
        relapsedAt: relapsedAt,
        healthCenterId: healthCenterId,
        description: description,
        createdAt: createdAt,
        updatedAt: updatedAt,
        survivor: survivor,
        healthCenter: healthCenter,
        relapse: relapse,
        creator: creator,
      );
    } catch (e) {
      // If deserialization fails, return basic Instance data as InstanceShow
      return InstanceShow(
        id: id ?? localId,
        code: code,
        type: InstanceType.values.firstWhere((t) => t.name == type),
        status: InstanceStatus.values.firstWhere((s) => s.name == status),
        survivorCode: survivorCode,
        relapsedAt: relapsedAt,
        healthCenterId: healthCenterId,
        description: description,
        createdAt: createdAt,
        updatedAt: updatedAt,
        survivor: null,
        healthCenter: null,
        relapse: null,
        creator: null,
      );
    }
  }

  // Update sync status
  void updateSyncStatus(SyncStatus status, {String? error}) {
    syncStatus = status.value;
    lastSyncAttempt = DateTime.now();
    syncError = error;
  }

  // Mark as synced with server ID
  void markAsSynced(String serverId) {
    id = serverId;
    syncStatus = SyncStatus.synced.value;
    lastSyncAttempt = DateTime.now();
    syncError = null;
  }

  // Check if needs sync
  bool get needsSync =>
      SyncStatus.fromString(syncStatus) == SyncStatus.pending ||
      SyncStatus.fromString(syncStatus) == SyncStatus.failed;

  // Check if is offline created
  bool get isOfflineCreated => id == null;

  // Get display ID
  String get displayId => id ?? localId;

  // Helper methods for clean JSON conversion
  static String? _survivorToJson(Survivor? survivor) {
    if (survivor == null) return null;
    final model = survivor is SurvivorModel
        ? survivor
        : SurvivorModel.fromEntity(survivor);
    return jsonEncode(model.toJson());
  }

  static String? _healthCenterToJson(HealthCenter? healthCenter) {
    if (healthCenter == null) return null;
    final model = healthCenter is HealthCenterModel
        ? healthCenter
        : HealthCenterModel.fromEntity(healthCenter);
    return jsonEncode(model.toJson());
  }

  static String? _relapseToJson(Relapse? relapse) {
    if (relapse == null) return null;
    final model =
        relapse is RelapseModel ? relapse : RelapseModel.fromEntity(relapse);
    return jsonEncode(model.toJson());
  }

  static String? _userToJson(User? user) {
    if (user == null) return null;
    final model = user is UserModel ? user : UserModel.fromEntity(user);
    return jsonEncode(model.toJson());
  }
}

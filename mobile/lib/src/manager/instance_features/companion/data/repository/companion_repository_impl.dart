import 'dart:developer';

import 'package:injectable/injectable.dart';
import 'package:s3g/core/errors/failures.dart';
import 'package:s3g/core/helpers/request_helper.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/network/connection_checker.dart';
import 'package:s3g/core/repository/repository.dart';
import 'package:s3g/src/manager/instance/data/local/instance_local_datasource.dart';
import 'package:s3g/src/manager/instance/domain/entity/instance.dart';
import 'package:s3g/src/manager/instance_features/companion/data/local/instance_companion_local_datasource.dart';
import 'package:s3g/src/manager/instance_features/companion/data/local/models/instance_companion_local_model.dart';
import 'package:s3g/src/manager/instance_features/companion/domain/entity/companion.dart';
import 'package:s3g/src/manager/member/member.dart';
import 'package:s3g/src/sync/sync_service.dart';
import 'package:fpdart/fpdart.dart';

import '../../domain/repository/companion_repository.dart';
import '../remote/companion_remote_datasource.dart';

@Injectable(as: CompanionRepository)
class CompanionRepositoryImpl extends CompanionRepository {
  final CompanionRemoteDataSource _remoteDataSource;
  final InstanceCompanionLocalDataSource _localDataSource;
  final ConnectionChecker _connectionChecker;
  final SyncService _syncService;
  final InstanceLocalDataSource _instanceLocalDataSource;
  final MemberLocalDataSource _memberLocalDataSource;

  CompanionRepositoryImpl(
    this._remoteDataSource,
    this._localDataSource,
    this._connectionChecker,
    this._syncService,
    this._instanceLocalDataSource,
    this._memberLocalDataSource,
  );

  @override
  RepositoryResponse<Companion> createCompanion({
    required Instance instance,
    required MemberCompanionRole type,
    required String userId,
  }) async {
    final isOnline = await _connectionChecker.isOnline();

    if (isOnline) {
      final response =
          await requestHelper(() => _remoteDataSource.createCompanion(
                instanceId: instance.id,
                type: type,
                userId: userId,
              ));

      if (response.isRight()) {
        final companion = response.fold(
          (l) => throw Exception(),
          (r) => r,
        );

        // Get instance local ID for storage
        final instanceModel =
            await _instanceLocalDataSource.getInstanceById(instance.id);

        if (instanceModel != null) {
          final localCompanion = InstanceCompanionLocalModel.fromEntity(
            companion,
            instanceLocalId: instanceModel.localId,
          );
          await _localDataSource.saveCompanion(localCompanion);
        }
      }

      return response;
    } else {
      // Create offline
      final member = await _memberLocalDataSource.getMemberById(
        userId,
        healthCenterId: instance.healthCenterId,
      );
      if (member == null) {
        return Left(CacheFailure('Membre introuvable'));
      }

      // Get instance by instanceId (which could be localId or server id)
      final instanceModel =
          await _instanceLocalDataSource.getInstanceById(instance.id);

      if (instanceModel == null) {
        return Left(CacheFailure('Instance introuvable'));
      }

      final localCompanion = InstanceCompanionLocalModel.createOffline(
        instanceLocalId: instanceModel.localId,
        type: type,
        user: member.toEntity(),
      );

      await _localDataSource.saveCompanion(localCompanion);

      return Right(localCompanion.toEntity());
    }
  }

  @override
  RepositoryResponse<MessageResponse> deleteCompanion({
    required Instance instance,
    required String companionId,
  }) async {
    final isOnline = await _connectionChecker.isOnline();

    if (isOnline) {
      final response =
          await requestHelper(() => _remoteDataSource.deleteCompanion(
                instanceId: instance.id,
                companionId: companionId,
              ));

      if (response.isRight()) {
        await _localDataSource.deleteCompanion(companionId);
      }

      return response;
    } else {
      // Delete offline
      await _localDataSource.markCompanionAsDeletedById(companionId);

      return Right(MessageResponse(message: 'Supprimé localement'));
    }
  }

  @override
  RepositoryResponse<List<Companion>> getCompanionList({
    required Instance instance,
  }) async {
    final isOnline = await _connectionChecker.isOnline();

    if (isOnline) {
      final response =
          await requestHelper(() => _remoteDataSource.getCompanionList(
                instanceId: instance.id,
              ));

      try {
        await _syncService.syncManagerCompanions();
      } catch (e) {
        log('Failed to sync companions before fetch: $e');
        // Continue with fetch even if sync fails
      }

      if (response.isRight()) {
        final companions = response.fold(
          (l) => <Companion>[],
          (r) => r,
        );

        // Get instance local ID for storage
        final instanceModel =
            await _instanceLocalDataSource.getInstanceById(instance.id);

        if (instanceModel != null) {
          final localCompanions = companions
              .map((companion) => InstanceCompanionLocalModel.fromEntity(
                    companion,
                    instanceLocalId: instanceModel.localId,
                  ))
              .toList();

          await _localDataSource.saveCompanions(localCompanions);
        }
      }

      return response;
    } else {
      // Get from local storage
      // instance.id could be localId or server id, so we need to get the instance first
      final instanceModel =
          await _instanceLocalDataSource.getInstanceById(instance.id);

      if (instanceModel == null) {
        return Left(CacheFailure('Instance introuvable'));
      }

      final localCompanions =
          await _localDataSource.getCompanionsByInstance(instanceModel.localId);

      final companions = localCompanions.map((c) => c.toEntity()).toList();

      return Right(companions);
    }
  }
}

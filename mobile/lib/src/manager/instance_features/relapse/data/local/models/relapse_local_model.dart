import 'package:objectbox/objectbox.dart';
import 'package:s3g/src/sync/sync_service.dart' show SyncStatus;
import 'package:uuid/uuid.dart';
import 'package:s3g/src/manager/instance_features/relapse/domain/entity/relapse.dart';

@Entity()
class InstanceRelapseLocalModel {
  @Id()
  int oid = 0;

  @Index()
  @Unique()
  String? id;

  @Index()
  @Unique()
  String localId;

  @Index()
  String instanceId;

  String description;

  // Sync metadata
  String syncStatus;
  DateTime? lastSyncAttempt;
  String? syncError;
  bool isDeleted;

  // Timestamps
  DateTime createdAt;
  DateTime updatedAt;

  InstanceRelapseLocalModel({
    this.id,
    required this.localId,
    required this.instanceId,
    required this.description,
    required this.syncStatus,
    this.lastSyncAttempt,
    this.syncError,
    this.isDeleted = false,
    required this.createdAt,
    required this.updatedAt,
  });

  // Factory method to create from entity
  factory InstanceRelapseLocalModel.fromEntity(
      Relapse relapse, String instanceId) {
    return InstanceRelapseLocalModel(
      id: relapse.id,
      localId: relapse.id,
      instanceId: instanceId,
      description: relapse.description,
      syncStatus: SyncStatus.synced.value,
      isDeleted: false,
      createdAt: relapse.createdAt,
      updatedAt: relapse.updatedAt,
    );
  }

  // Factory method to create offline instance
  factory InstanceRelapseLocalModel.createOffline({
    required String instanceId,
    required String description,
  }) {
    final now = DateTime.now();
    return InstanceRelapseLocalModel(
      localId: const Uuid().v4(),
      instanceId: instanceId,
      description: description,
      syncStatus: SyncStatus.pending.value,
      isDeleted: false,
      createdAt: now,
      updatedAt: now,
    );
  }

  // Convert to entity
  Relapse toEntity() {
    return Relapse(
      id: id ?? localId,
      description: description,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  // Clone with updates
  InstanceRelapseLocalModel copyWith({
    String? id,
    String? description,
    String? syncStatus,
    DateTime? lastSyncAttempt,
    String? syncError,
    bool? isDeleted,
    DateTime? updatedAt,
  }) {
    return InstanceRelapseLocalModel(
      id: id ?? this.id,
      localId: localId,
      instanceId: instanceId,
      description: description ?? this.description,
      syncStatus: syncStatus ?? this.syncStatus,
      lastSyncAttempt: lastSyncAttempt ?? this.lastSyncAttempt,
      syncError: syncError ?? this.syncError,
      isDeleted: isDeleted ?? this.isDeleted,
      createdAt: createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Check if needs sync
  bool get needsSync =>
      SyncStatus.fromString(syncStatus) == SyncStatus.pending ||
      SyncStatus.fromString(syncStatus) == SyncStatus.failed;

  // Check if is offline created
  bool get isOfflineCreated => id == null;

  // Get display ID
  String get displayId => id ?? localId;

  // Update sync status
  void updateSyncStatus(SyncStatus status, {String? error}) {
    syncStatus = status.value;
    lastSyncAttempt = DateTime.now();
    syncError = error;
  }

  // Mark as synced with server ID
  void markAsSynced(String serverId) {
    id = serverId;
    syncStatus = SyncStatus.synced.value;
    lastSyncAttempt = DateTime.now();
    syncError = null;
  }

  // Update content and mark for sync
  void updateContent({String? newDescription}) {
    if (newDescription != null) description = newDescription;
    updatedAt = DateTime.now();

    // Mark as needing sync if already synced
    if (SyncStatus.fromString(syncStatus) == SyncStatus.synced) {
      syncStatus = SyncStatus.pending.value;
    }
  }
}

import 'dart:developer';

import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';
import 'package:s3g/core/errors/failures.dart';
import 'package:s3g/core/helpers/request_helper.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/repository/repository.dart';
import 'package:s3g/core/network/connection_checker.dart';

import 'package:s3g/src/manager/instance_features/relapse/domain/entity/relapse.dart';

import '../../domain/repository/relapse_repository.dart';
import '../remote/relapse_remote_datasource.dart';
import '../local/relapse_local_datasource.dart';
import '../local/models/relapse_local_model.dart';

@Injectable(as: RelapseRepository)
class RelapseRepositoryImpl implements RelapseRepository {
  final RelapseRemoteDataSource _relapseRemoteDataSource;
  final InstanceRelapseLocalDataSource _localDataSource;
  final ConnectionChecker _connectionChecker;

  RelapseRepositoryImpl(
    this._relapseRemoteDataSource,
    this._localDataSource,
    this._connectionChecker,
  );

  @override
  RepositoryResponse<Relapse> createRelapse({
    required String instanceId,
    required String description,
  }) async {
    try {
      // Check if device is online
      final isOnline = await _connectionChecker.isOnline();

      if (isOnline) {
        // Try to create on server first
        final response =
            await requestHelper(() => _relapseRemoteDataSource.createRelapse(
                  instanceId: instanceId,
                  description: description,
                ));

        return response.fold(
          (failure) => Left(failure),
          (relapse) async {
            // Save to local storage as synced
            final localRelapse =
                InstanceRelapseLocalModel.fromEntity(relapse, instanceId);
            await _localDataSource.saveRelapse(localRelapse);
            return Right(relapse);
          },
        );
      } else {
        // Create offline
        final localRelapse = InstanceRelapseLocalModel.createOffline(
          instanceId: instanceId,
          description: description,
        );
        await _localDataSource.saveRelapse(localRelapse);

        return Right(localRelapse.toEntity());
      }
    } catch (e) {
      log('Error creating relapse: $e');
      return Left(DatabaseFailure('Erreur lors de la création de la rechute'));
    }
  }

  @override
  RepositoryResponse<MessageResponse> deleteRelapse({
    required String instanceId,
  }) async {
    try {
      // Check if device is online
      final isOnline = await _connectionChecker.isOnline();

      if (isOnline) {
        // Try to delete on server first
        final response =
            await requestHelper(() => _relapseRemoteDataSource.deleteRelapse(
                  instanceId: instanceId,
                ));

        return response.fold(
          (failure) => Left(failure),
          (messageResponse) async {
            // Delete from local storage
            final existingRelapse =
                await _localDataSource.getRelapseByInstanceId(instanceId);
            if (existingRelapse != null) {
              await _localDataSource.deleteRelapse(existingRelapse.localId);
            }
            return Right(messageResponse);
          },
        );
      } else {
        // Mark as deleted offline
        final existingRelapse =
            await _localDataSource.getRelapseByInstanceId(instanceId);
        if (existingRelapse != null) {
          await _localDataSource.markRelapseAsDeleted(existingRelapse.localId);
          return Right(
              MessageResponse(message: 'Rechute supprimée hors ligne'));
        } else {
          return Left(
              CacheFailure('Rechute introuvable pour suppression hors ligne'));
        }
      }
    } catch (e) {
      log('Error deleting relapse: $e');
      return Left(
          DatabaseFailure('Erreur lors de la suppression de la rechute'));
    }
  }

  @override
  RepositoryResponse<Relapse> getRelapse({required String instanceId}) async {
    try {
      // Check if device is online
      final isOnline = await _connectionChecker.isOnline();

      if (isOnline) {
        // Try to get from server first
        final response =
            await requestHelper(() => _relapseRemoteDataSource.getRelapse(
                  instanceId: instanceId,
                ));

        return response.fold(
          (failure) async {
            // If server fails, try local storage
            final localRelapse =
                await _localDataSource.getRelapseByInstanceId(instanceId);
            if (localRelapse != null && !localRelapse.isDeleted) {
              return Right(localRelapse.toEntity());
            }
            return Left(failure);
          },
          (relapse) async {
            // Save to local storage as synced
            final localRelapse =
                InstanceRelapseLocalModel.fromEntity(relapse, instanceId);
            await _localDataSource.saveRelapse(localRelapse);
            return Right(relapse);
          },
        );
      } else {
        // Get from local storage
        final localRelapse =
            await _localDataSource.getRelapseByInstanceId(instanceId);
        if (localRelapse != null && !localRelapse.isDeleted) {
          return Right(localRelapse.toEntity());
        }
        return Left(CacheFailure('Aucune rechute trouvée hors ligne'));
      }
    } catch (e) {
      log('Error getting relapse: $e');
      return Left(
          DatabaseFailure('Erreur lors de la récupération de la rechute'));
    }
  }

  @override
  RepositoryResponse<Relapse> updateRelapse({
    required String instanceId,
    required String description,
  }) async {
    try {
      // Check if device is online
      final isOnline = await _connectionChecker.isOnline();

      if (isOnline) {
        // Try to update on server first
        final response =
            await requestHelper(() => _relapseRemoteDataSource.updateRelapse(
                  instanceId: instanceId,
                  description: description,
                ));

        return response.fold(
          (failure) => Left(failure),
          (relapse) async {
            // Update local storage as synced
            final localRelapse =
                InstanceRelapseLocalModel.fromEntity(relapse, instanceId);
            await _localDataSource.saveRelapse(localRelapse);
            return Right(relapse);
          },
        );
      } else {
        // Update offline
        final existingRelapse =
            await _localDataSource.getRelapseByInstanceId(instanceId);
        if (existingRelapse != null) {
          await _localDataSource.updateRelapseContent(
            existingRelapse.localId,
            description: description,
          );
          final updatedRelapse = await _localDataSource
              .getRelapseByLocalId(existingRelapse.localId);
          return Right(updatedRelapse!.toEntity());
        } else {
          return Left(
              CacheFailure('Rechute introuvable pour modification hors ligne'));
        }
      }
    } catch (e) {
      log('Error updating relapse: $e');
      return Left(
          DatabaseFailure('Erreur lors de la modification de la rechute'));
    }
  }
}

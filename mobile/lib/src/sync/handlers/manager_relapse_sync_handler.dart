import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:s3g/src/manager/instance/data/local/instance_local_datasource.dart';
import 'package:s3g/src/manager/instance_features/relapse/data/local/relapse_local_datasource.dart';
import 'package:s3g/src/manager/instance_features/relapse/data/local/models/relapse_local_model.dart';
import 'package:s3g/src/manager/instance_features/relapse/data/remote/relapse_remote_datasource.dart';
import 'package:s3g/src/sync/handlers/base_sync_handler.dart';
import 'package:s3g/src/sync/sync_service.dart';

@injectable
class ManagerRelapseSyncHandler extends BaseSyncHandler {
  final InstanceRelapseLocalDataSource _localDataSource;
  final RelapseRemoteDataSource _remoteDataSource;
  final InstanceLocalDataSource _instanceLocalDataSource;

  ManagerRelapseSyncHandler({
    required InstanceRelapseLocalDataSource localDataSource,
    required RelapseRemoteDataSource remoteDataSource,
    required InstanceLocalDataSource instanceLocalDataSource,
  })  : _localDataSource = localDataSource,
        _remoteDataSource = remoteDataSource,
        _instanceLocalDataSource = instanceLocalDataSource;

  @override
  String get featureName => 'Manager Relapse';

  @override
  Future<void> syncAll() async {
    log('Starting $featureName sync...');

    try {
      // Get all pending relapses
      final pendingRelapses = await _localDataSource.getPendingSyncRelapses();
      log('Found ${pendingRelapses.length} relapses to sync');

      for (final localRelapse in pendingRelapses) {
        await _syncSingleRelapse(localRelapse);
      }

      log('$featureName sync completed');
    } catch (e) {
      log('$featureName sync failed: $e');
      rethrow;
    }
  }

  Future<void> _syncSingleRelapse(InstanceRelapseLocalModel localRelapse) async {
    try {
      // Get the instance to check if it's synced
      // Try by localId first, then by server ID
      var instance = await _instanceLocalDataSource.getInstanceByLocalId(
        localRelapse.instanceId,
      );

      // If not found by localId, try by server ID
      if (instance == null) {
        instance = await _instanceLocalDataSource.getInstanceById(
          localRelapse.instanceId,
        );
      }

      if (instance == null) {
        log('Instance not found for relapse ${localRelapse.localId}');
        return;
      }

      // CRITICAL: Check if instance is synced before syncing relapse
      if (instance.syncStatus != SyncStatus.synced.value) {
        log('Instance ${instance.localId} not synced yet (status: ${instance.syncStatus}), skipping relapse ${localRelapse.localId}');
        return;
      }

      if (localRelapse.isDeleted) {
        // Handle deletion
        if (localRelapse.id != null) {
          // Delete from server if it exists there
          log('Deleting relapse ${localRelapse.id} from server');
          try {
            await _remoteDataSource.deleteRelapse(
              instanceId: instance.id!, // Use server ID for API
            );
          } on DioException catch (e) {
            if (e.response?.statusCode != 404) {
              rethrow;
            }
            // Ignore 404 errors - relapse already deleted on server
          } catch (e) {
            rethrow;
          }
        }
        // Physical removal from local storage
        await _localDataSource.deleteRelapse(localRelapse.localId);
        return;
      }

      if (localRelapse.isOfflineCreated) {
        // Create new relapse on server
        log('Creating new relapse on server for instance: ${instance.id}');

        final result = await _remoteDataSource.createRelapse(
          instanceId: instance.id!, // Use server ID for API
          description: localRelapse.description,
        );

        // Use helper method to mark as synced
        await _localDataSource.markRelapseAsSynced(
          localRelapse.localId,
          result.id,
        );

        log('Relapse created successfully with ID: ${result.id}');
      } else {
        // Update existing relapse on server
        log('Updating relapse on server for instance: ${instance.id}');

        await _remoteDataSource.updateRelapse(
          instanceId: instance.id!, // Use server ID for API
          description: localRelapse.description,
        );

        // Use helper method to mark as synced
        localRelapse.updateSyncStatus(SyncStatus.synced);
        await _localDataSource.saveRelapse(localRelapse);

        log('Relapse updated successfully');
      }
    } catch (e) {
      log('Failed to sync relapse ${localRelapse.localId}: $e');

      // Use helper method to mark sync as failed
      await _localDataSource.markRelapseSyncFailed(
        localRelapse.localId,
        e.toString(),
      );
    }
  }
}
